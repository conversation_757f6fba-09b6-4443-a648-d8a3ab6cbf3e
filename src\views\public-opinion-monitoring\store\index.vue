<template>
  <div class="page-container" ref="pageContainer">
    <!-- 顶部游戏选择与方案栏 -->
    <game-title @plan-update="handlePlanUpdate" :show-plan-section="false" />

    <!-- 主要内容区域 -->
    <a-row :gutter="20" class="main-content">
      <!-- 左侧：游戏评分与趋势 -->
      <a-col :span="16" class="left-panel">
        <!-- 游戏评分区域 -->
        <a-card title="游戏评分" class="score-section" :bordered="false">
          <template #extra>
            <a-space>
              <DatePicker
                v-model="scoreDate"
                picker-type="score"
                @change="onRangeChange"
              />
              <a-select
                v-model:value="selectedCountry"
                placeholder="所有国家/地区"
                class="score-select"
                :options="countryOptions"
                :loading="countryLoading"
                size="small"
                style="width: 140px"
                :getPopupContainer="getPopupContainer"
                @popupScroll="handleCountryScroll"
              />
              <a-select
                v-model:value="selectedStore"
                placeholder="所有商店"
                class="score-select"
                :options="storeOptions"
                :loading="storeLoading"
                size="small"
                style="width: 120px"
                :getPopupContainer="getPopupContainer"
              />
              <!-- 调试信息 -->
              <div style="font-size: 12px; color: #666; margin-top: 5px;">
                调试: 商店选项数量={{ storeOptions.length }}, 加载状态={{ storeLoading }}
                <br>选项内容: {{ JSON.stringify(storeOptions) }}
                <br>当前选中值: {{ selectedStore }}
              </div>
            </a-space>
          </template>

          <a-row :gutter="40" class="score-main" v-loading="scoreLoading">
            <a-col :span="8" class="score-value-section">
              <div class="score-value">{{ scoreData.avgScore.toFixed(1) }}</div>
              <a-rate :value="scoreData.avgScore" disabled allow-half class="score-rate" />
              <div class="score-summary">
                {{ formatNumber(scoreData.participantsNumber) }}人参与了评分
                {{ calculateGoodRating() }}%好评率
              </div>
            </a-col>
            <a-col :span="16" class="score-stars">
              <div class="star-bar" v-for="i in 5" :key="i">
                <span class="star-label">{{ 6 - i }}星</span>
                <a-progress
                  :percent="starWidths[i - 1]"
                  :show-info="false"
                  :stroke-color="{
                    '0%': '#29ade6',
                    '100%': '#60a5fa'
                  }"
                  class="star-progress"
                />
                <span class="star-count">{{ starCounts[i - 1] }}</span>
              </div>
            </a-col>
          </a-row>
        </a-card>

        <!-- 评分趋势区域 -->
        <a-card title="评分趋势" class="trend-section" :bordered="false">
          <template #extra>
            <DatePicker
              v-model="trendDate"
              picker-type="trend"
              @change="onTrendRangeChange"
            />
          </template>

          <div class="trend-chart">
            <LineChart
              :chart-data="trendChartData"
              :color-list="['#ff6b6b', '#ffa726', '#ffeb3b', '#66bb6a', '#42a5f5']"
              height="450px"
            />
          </div>
        </a-card>
      </a-col>

      <!-- 右侧：评论详情 -->
      <a-col :span="8" class="right-panel">
        <a-card class="comment-section" :bordered="false">
          <!-- 评论详情标题独立出来 -->
          <div class="comment-title-section">
            <span class="comment-title">评论详情</span>
          </div>
          <!-- 新增：筛选行（日期选择器和商店选择器） -->
          <div class="comment-filter-row">
            <a-space>
              <DatePicker
                v-model="commentDate"
                picker-type="comment"
                @change="onCommentRangeChange"
              />
              <a-select
                v-model:value="selectedCommentStore"
                placeholder="所有商店"
                class="comment-select"
                :options="storeOptions"
                :loading="storeLoading"
                size="small"
                style="width: 120px"
                :getPopupContainer="getPopupContainer"
              />
            </a-space>
          </div>
          <!-- 搜索区域 -->
          <div class="comment-search-row">
            <a-input
              v-model:value="commentKeyword"
              placeholder="输入关键词，输入多个请用空格分隔，最多输入100个字符"
              class="comment-keyword-input"
              @pressEnter="handleCommentSearch"
            />
            <a-button type="primary" @click="handleCommentSearch" class="comment-search-btn">
              搜索
            </a-button>
          </div>
          <!-- 标签切换 -->
          <a-tabs v-model:activeKey="activeTab" class="comment-tabs" @change="handleTabChange">
            <a-tab-pane key="highest" tab="评分最高" />
            <a-tab-pane key="latest" tab="最新评论" />
            <template #rightExtra>
              <a-button type="link" class="comment-more" @click="handleViewMore">
                查看更多
              </a-button>
            </template>
          </a-tabs>
          <!-- 评论列表 -->
          <a-list
            :data-source="commentList"
            :loading="commentLoading"
            class="comment-list"
            :split="false"
          >
            <template #renderItem="{ item }">
              <a-list-item class="comment-item">
                <a-list-item-meta>
                  <template #description>
                    <div class="comment-content">{{ item.content }}</div>
                    <div class="comment-footer">
                      <div class="comment-rating">
                        <a-rate :value="item.rating" disabled allow-half size="small" />
                        <span class="rating-text">{{ item.rating }}</span>
                      </div>
                      <div class="comment-date">{{ item.date }}</div>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
          <!-- 分页组件 -->
          <div class="comment-pagination" v-if="commentPagination.total > 0">
            <a-pagination
              v-model:current="commentPagination.current"
              v-model:page-size="commentPagination.pageSize"
              :total="commentPagination.total"
              :show-size-changer="false"
              :show-quick-jumper="false"
              :show-total="(total, range) => `共 ${total} 条评论`"
              size="small"
              @change="handlePageChange"
            />
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import gameTitle from '../components/gameTitle.vue';
import LineChart from '../components/chart/LineChart.vue';
import { ref, onMounted, nextTick, watch } from 'vue';
import dayjs, { Dayjs } from 'dayjs';
import { getOpinionStoreScoreApi, getOpinionStoreTrendApi, getOpinionStoreCommentScoreApi, getOpinionStoreCommentDayApi, getOpinionStoreCountryApi ,getOpinionStoreDeviceApi} from '@/api/public-opinion-monitoring/store';
import DatePicker from '../components/datePicker.vue';

// 创建页面容器引用，用于下拉菜单定位
const pageContainer = ref(null);

// 响应式数据
const scoreDate = ref('');
const trendDate = ref('');
const commentDate = ref('');
const selectedCountry = ref(null);
const selectedStore = ref(null);
const selectedCommentStore = ref(null);
const commentKeyword = ref('');
const activeTab = ref('highest');

// 游戏ID - 从localStorage获取
const gameId = ref(localStorage.getItem('gameId') || '');

// 评分数据
const scoreData = ref({
  avgScore: 0,
  totalCount: 0,
  participantsNumber: 0,
  oneStar: 0,
  twoStar: 0,
  threeStar: 0,
  fourStar: 0,
  fiveStar: 0
});

// 加载状态
const scoreLoading = ref(false);
const trendLoading = ref(false);
const commentLoading = ref(false);

// 折线图数据 - 初始化为空数据
const trendChartData = ref({
  xAxisData: [],
  seriesData: [
    { name: '1星', data: [] },
    { name: '2星', data: [] },
    { name: '3星', data: [] },
    { name: '4星', data: [] },
    { name: '5星', data: [] }
  ],
  legendData: ['1星', '2星', '3星', '4星', '5星'],
  yAxisName: '评分数量'
});

// 选项数据
const countryOptions = ref([
  { label: '所有国家/地区', value: 'all' }
]);

// 国家数据加载状态
const countryLoading = ref(false);

// 国家数据分页信息
const countryPagination = ref({
  current: 1,
  pageSize: 50,
  total: 0,
  pages: 0,
  hasMore: true
});

const storeOptions = ref([
  { label: '所有商店', value: 'all' }
]);

// 商店数据加载状态
const storeLoading = ref(false);

// 评分数据 - 动态计算
const starCounts = ref([0, 0, 0, 0, 0]);
const starWidths = ref([0, 0, 0, 0, 0]);

// 评论数据和分页信息
const commentList = ref([]);
const commentPagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  pages: 0
});

// 原始评论数据（用于搜索筛选）
const originalCommentList = ref([]);

// 评论搜索关键词（处理后的）
const processedKeywords = ref('');

// 日期选择器的便捷选项移至 DatePicker 组件中

// 日期范围变化处理函数
const onRangeChange = (dates) => {
  if (dates) {
    // 日期范围变化时的处理逻辑
    // 重新获取评分数据
    fetchScoreData();
  }
};

// 趋势日期范围变化处理函数
const onTrendRangeChange = (dates) => {
  if (dates) {
    // 重新获取趋势数据
    fetchTrendData();
  }
};

// 评论日期范围变化处理函数
const onCommentRangeChange = (dates) => {
  if (dates) {
    // 重置分页并重新获取评论数据
    commentPagination.value.current = 1;
    fetchCommentData();
  }
};

// 获取商店数据
const fetchStoreData = async () => {
  try {
    storeLoading.value = true;
    console.log('开始获取商店数据...');

    const response = await getOpinionStoreDeviceApi();
    console.log('商店API返回数据:', response);

    // 处理返回值，构建商店选项
    const storeOptionsList = [
      { label: '所有商店', value: 'all' }
    ];

    // 添加从接口获取的商店数据
    if (response && Array.isArray(response)) {
      console.log('处理商店数据，数组长度:', response.length);
      response.forEach(item => {
        // 将apple转换为App Store显示，但value保持原值
        const label = item.value === 'apple' ? 'App Store' :
                     item.value === 'google' ? 'Google Play' :
                     item.label || item.value;

        storeOptionsList.push({
          label: label,
          value: item.value
        });
      });
    } else {
      console.log('商店数据格式不正确:', { response, isArray: Array.isArray(response) });
    }

    console.log('最终商店选项列表:', storeOptionsList);
    console.log('详细商店选项:', JSON.stringify(storeOptionsList, null, 2));
    storeOptions.value = storeOptionsList;
    console.log('storeOptions.value 设置后:', storeOptions.value);

    // 强制触发响应式更新
    await nextTick();
    console.log('nextTick后 storeOptions.value:', storeOptions.value);

  } catch (error) {
    console.error('获取商店数据失败:', error);
    // 如果获取失败，保持默认的"所有商店"选项
  } finally {
    storeLoading.value = false;
  }
};

// 获取国家数据（单页）
const fetchCountryData = async (reset = false) => {
  if (countryLoading.value) return; // 防止重复调用

  try {
    countryLoading.value = true;

    // 如果是重置，则重置分页信息
    if (reset) {
      countryPagination.value.current = 1;
      countryPagination.value.hasMore = true;
      // 重置选项，只保留"所有国家/地区"
      countryOptions.value = [{ label: '所有国家/地区', value: 'all' }];
    }

    // 如果没有更多数据，直接返回
    if (!countryPagination.value.hasMore) {
      return;
    }

    const params = {
      pageNo: countryPagination.value.current,
      pageSize: countryPagination.value.pageSize
    };

    const response = await getOpinionStoreCountryApi(params);

    // 修复：直接使用response，而不是response.result
    if (response && response.records) {
      const countries = response.records;

      // 更新分页信息
      countryPagination.value.total = response.total || 0;
      countryPagination.value.pages = response.pages || 1;
      countryPagination.value.hasMore = countryPagination.value.current < countryPagination.value.pages;

      // 构建新的国家选项
      const newCountryOptions = countries.map(country => ({
        label: country.countryCn,
        value: country.countryId
      }));

      // 添加到现有选项中（排除"所有国家/地区"）
      const existingOptions = countryOptions.value.slice(1); // 去掉第一个"所有国家/地区"
      const allNewOptions = [...existingOptions, ...newCountryOptions];

      // 按中文名排序
      allNewOptions.sort((a, b) => a.label.localeCompare(b.label, 'zh-CN'));

      // 重新构建完整的选项列表
      countryOptions.value = [
        { label: '所有国家/地区', value: 'all' },
        ...allNewOptions
      ];

      // 更新当前页码
      countryPagination.value.current++;

    } else {
      countryPagination.value.hasMore = false;
    }

  } catch (error) {
    // console.error('获取国家数据失败:', error);
    countryPagination.value.hasMore = false;
  } finally {
    countryLoading.value = false;
  }
};

// 获取评分数据
const fetchScoreData = async () => {
  try {
    scoreLoading.value = true;

    if (!gameId.value) {
      return;
    }

    // 构建请求参数
    const params = {
      gameId: gameId.value,
    };

    // 添加时间范围参数
    if (scoreDate.value && scoreDate.value.length === 2) {
      params.startTime = dayjs(scoreDate.value[0]).valueOf();
      params.endTime = dayjs(scoreDate.value[1]).valueOf();

    }

    // 添加国家参数
    if (selectedCountry.value && selectedCountry.value !== 'all') {
      params.country = selectedCountry.value;
    }

    // 添加商店参数
    if (selectedStore.value && selectedStore.value !== 'all') {
      params.store = selectedStore.value;
    }


    const response = await getOpinionStoreScoreApi(params);

    if (response && response.result) {
      const result = response.result;

      // 更新评分数据
      scoreData.value = {
        avgScore: result.avgScore || 0,
        totalCount: result.totalCount || 0,
        participantsNumber: result.participantsNumber || 0,
        oneStar: result.oneStar || 0,
        twoStar: result.twoStar || 0,
        threeStar: result.threeStar || 0,
        fourStar: result.fourStar || 0,
        fiveStar: result.fiveStar || 0
      };

      // 计算星级数据
      calculateStarData();

    }
  } catch (error) {
    // 如果是数据库字段错误，给出友好提示
    if (error.message && error.message.includes('Unknown column')) {
    }
  } finally {
    scoreLoading.value = false;
  }
};

// 获取趋势数据
const fetchTrendData = async () => {
  try {
    trendLoading.value = true;

    if (!gameId.value) {
      // 清空图表数据
      processTrendData(null);
      return;
    }

    // 构建请求参数
    const params = {
      gameId: gameId.value,
    };

    // 添加时间范围参数
    if (trendDate.value && trendDate.value.length === 2) {
      params.startTime = dayjs(trendDate.value[0]).valueOf();
      params.endTime = dayjs(trendDate.value[1]).valueOf();


    }


    const response = await getOpinionStoreTrendApi(params);

    if (response && response.result) {
      const result = response.result;

      // 处理趋势数据
      processTrendData(result);

    } else {
      // 如果没有返回数据，清空图表
      processTrendData(null);
    }
  } catch (error) {
    // 发生错误时也清空图表
    processTrendData(null);
  } finally {
    trendLoading.value = false;
  }
};

// 处理趋势数据，转换为图表需要的格式
const processTrendData = (data) => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    // 如果没有数据，使用默认的空数据
    trendChartData.value = {
      xAxisData: [],
      seriesData: [
        { name: '1星', data: [] },
        { name: '2星', data: [] },
        { name: '3星', data: [] },
        { name: '4星', data: [] },
        { name: '5星', data: [] }
      ],
      legendData: ['1星', '2星', '3星', '4星', '5星'],
      yAxisName: '评分数量'
    };
    return;
  }

  // 提取日期作为X轴数据
  const xAxisData = data.map(item => dayjs(item.day).format('MM-DD'));

  // 提取各星级数据
  const oneStarData = data.map(item => parseInt(item.oneStar) || 0);
  const twoStarData = data.map(item => parseInt(item.twoStar) || 0);
  const threeStarData = data.map(item => parseInt(item.threeStar) || 0);
  const fourStarData = data.map(item => parseInt(item.fourStar) || 0);
  const fiveStarData = data.map(item => parseInt(item.fiveStar) || 0);

  // 更新图表数据
  trendChartData.value = {
    xAxisData: xAxisData,
    seriesData: [
      { name: '1星', data: oneStarData },
      { name: '2星', data: twoStarData },
      { name: '3星', data: threeStarData },
      { name: '4星', data: fourStarData },
      { name: '5星', data: fiveStarData }
    ],
    legendData: ['1星', '2星', '3星', '4星', '5星'],
    yAxisName: '评分数量'
  };
};

// 获取评论数据
const fetchCommentData = async () => {
  try {
    commentLoading.value = true;

    if (!gameId.value) {
      // 清空评论列表
      commentList.value = [];
      commentPagination.value = {
        current: 1,
        pageSize: 10,
        total: 0,
        pages: 0
      };
      return;
    }

    // 构建请求参数
    const params = {
      gameId: gameId.value,
      page: commentPagination.value.current,
      size: commentPagination.value.pageSize,
    };

    // 添加时间范围参数
    if (commentDate.value && commentDate.value.length === 2) {
      params.startTime = dayjs(commentDate.value[0]).valueOf();
      params.endTime = dayjs(commentDate.value[1]).valueOf();
    }

    // 添加商店参数
    if (selectedCommentStore.value && selectedCommentStore.value !== 'all') {
      params.store = selectedCommentStore.value;
    }

    // 添加关键词参数
    if (processedKeywords.value) {
      params.keywords = processedKeywords.value;
    }

    // 根据当前标签选择不同的API
    let response;
    if (activeTab.value === 'highest') {
      // 获取评分最高的评论
      response = await getOpinionStoreCommentScoreApi(params);
    } else if (activeTab.value === 'latest') {
      // 获取最新评论
      response = await getOpinionStoreCommentDayApi(params);
    } else {
      // 默认获取评分最高的评论
      response = await getOpinionStoreCommentScoreApi(params);
    }

    if (response && response.result) {
      const result = response.result;

      // 处理评论数据
      const comments = (result.records || []).map(item => ({
        id: Math.random(), // 临时ID，因为接口没有返回ID
        content: item.title || '', // 使用title作为评论内容
        rating: item.score || 0,
        date: item.createTime || ''
      }));

      commentList.value = comments;

      // 更新分页信息
      commentPagination.value = {
        current: result.current || 1,
        pageSize: result.size || 10,
        total: result.total || 0,
        pages: result.pages || 0
      };

    } else {
      // 如果没有返回数据，清空列表
      commentList.value = [];
      commentPagination.value = {
        current: 1,
        pageSize: 10,
        total: 0,
        pages: 0
      };
    }
  } catch (error) {
    // 发生错误时清空列表
    commentList.value = [];
    commentPagination.value = {
      current: 1,
      pageSize: 10,
      total: 0,
      pages: 0
    };
  } finally {
    commentLoading.value = false;
  }
};

// 计算星级数据
const calculateStarData = () => {
  const { oneStar, twoStar, threeStar, fourStar, fiveStar, totalCount } = scoreData.value;

  // 更新星级数量（从5星到1星的顺序）
  starCounts.value = [fiveStar, fourStar, threeStar, twoStar, oneStar];

  // 计算星级百分比（用于进度条宽度）
  if (totalCount > 0) {
    starWidths.value = [
      Math.round((fiveStar / totalCount) * 100),
      Math.round((fourStar / totalCount) * 100),
      Math.round((threeStar / totalCount) * 100),
      Math.round((twoStar / totalCount) * 100),
      Math.round((oneStar / totalCount) * 100)
    ];
  } else {
    starWidths.value = [0, 0, 0, 0, 0];
  }
};

// 获取弹出容器 - 参考搜索页面的实现
const getPopupContainer = () => {
  return pageContainer.value || document.body;
};

// 格式化数字显示
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万';
  }
  return num.toString();
};

// 计算好评率（4星和5星的比例）
const calculateGoodRating = () => {
  const { fourStar, fiveStar, totalCount } = scoreData.value;
  if (totalCount > 0) {
    return Math.round(((fourStar + fiveStar) / totalCount) * 100);
  }
  return 0;
};

// 事件处理方法
const handlePlanUpdate = (data) => {
  // 这里可以根据方案更新重新加载数据
};

const handleCommentSearch = () => {
  // 使用空格分隔关键词
  const keywords = commentKeyword.value.trim().split(/\s+/).filter(keyword => keyword.length > 0);

  // 处理关键词：用空格连接，传递给后端
  processedKeywords.value = keywords.join(' ');

  // 重置分页并重新获取数据
  commentPagination.value.current = 1;
  fetchCommentData();
};

const handleTabChange = (tab) => {
  activeTab.value = tab;

  // 清空搜索关键词
  commentKeyword.value = '';
  processedKeywords.value = '';

  // 重置分页
  commentPagination.value.current = 1;

  // 只有在有游戏ID的情况下才获取评论数据
  if (gameId.value) {
    // 根据标签切换获取不同的评论数据
    if (tab === 'highest') {
      // 获取评分最高的评论 (调用 getOpinionStoreCommentScoreApi)
      fetchCommentData();
    } else if (tab === 'latest') {
      // 获取最新评论 (调用 getOpinionStoreCommentDayApi)
      fetchCommentData();
    }
  } else {
    // 如果没有游戏ID，清空评论列表
    commentList.value = [];
    commentPagination.value = {
      current: 1,
      pageSize: 10,
      total: 0,
      pages: 0
    };
  }
};

const handleViewMore = () => {
  // 这里实现查看更多评论的逻辑
};

// 分页变化处理函数
const handlePageChange = (page, pageSize) => {
  commentPagination.value.current = page;
  if (pageSize) {
    commentPagination.value.pageSize = pageSize;
  }
  fetchCommentData();
};

// 国家下拉框滚动处理函数
const handleCountryScroll = (e) => {
  const { target } = e;
  // 当滚动到底部时加载更多数据
  if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
    if (countryPagination.value.hasMore && !countryLoading.value) {
      fetchCountryData(false); // 不重置，加载下一页
    }
  }
};

// 监听游戏ID变化
window.addEventListener('gameIdChanged', (event) => {
  const newGameId = event.detail;
  if (newGameId !== gameId.value) {
    gameId.value = newGameId || '';
    // 游戏变化时重新获取所有数据
    if (gameId.value) {
      fetchScoreData();
      fetchTrendData();
      // 重置评论分页并获取评论数据
      commentPagination.value.current = 1;
      fetchCommentData();
    }
  }
});

// 监听localStorage中gameId的变化
window.addEventListener('storage', (event) => {
  if (event.key === 'gameId' && event.newValue !== gameId.value) {
    gameId.value = event.newValue || '';
    if (gameId.value) {
      fetchScoreData();
      fetchTrendData();
      // 重置评论分页并获取评论数据
      commentPagination.value.current = 1;
      fetchCommentData();
    }
  }
});

// 监听筛选条件变化
watch([selectedCountry, selectedStore], () => {
  if (gameId.value) {
    fetchScoreData();
  }
}, { deep: true });

// 监听评论商店选择器变化
watch(selectedCommentStore, () => {
  if (gameId.value) {
    // 重置分页并重新获取评论数据
    commentPagination.value.current = 1;
    fetchCommentData();
  }
}, { deep: true });

// 组件挂载时初始化数据
onMounted(() => {
  // 获取商店数据
  fetchStoreData();

  // 获取国家数据（第一页）
  fetchCountryData(true);

  // 默认显示评分最高的评论 (handleTabChange内部会调用fetchCommentData)
  handleTabChange('highest');

  // 如果有游戏ID，获取评分数据和趋势数据
  if (gameId.value) {
    fetchScoreData();
    fetchTrendData();
    // 注意：不需要再调用fetchCommentData，因为handleTabChange已经调用了
  } else {
    // 如果没有游戏ID，确保图表和评论显示为空
    processTrendData(null);
    commentList.value = [];
  }
});


</script>

<style scoped>
.page-container {
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  width: 100%;
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.main-content {
  margin-top: 20px;
  align-items: stretch; /* 确保左右两列高度一致 */
}

.main-content :deep(.ant-col) {
  display: flex;
  flex-direction: column;
}

.left-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.right-panel {
  display: flex;
  flex-direction: column;
  /* height: 100%; 移除固定高度，让它自适应左侧 */
}

/* Ant Design Card 样式覆盖 */
:deep(.ant-card) {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #e8eaec;
}

:deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

/* 统一卡片标题样式 - 参考today页面样式 */
:deep(.ant-card-head-title) {
  font-size: 20px;
  font-weight: bold;
  margin: 0;
  color: #333;
  border-bottom: 4px solid #018ffb;
  padding-bottom: 4px;
  display: inline-block;
  width: fit-content;
  max-width: 100px;
}


:deep(.ant-card-body) {
  padding: 24px;
}

/* 评论详情卡片的特殊样式 - 提高优先级 */
.comment-section.comment-section :deep(.ant-card-body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 24px;
}

/* 游戏评分区域样式 */
.score-section {
  margin-bottom: 20px;
}

.score-main {
  margin-top: 16px;
}

.score-value-section {
  text-align: center;
}

.score-value {
  font-size: 72px;
  font-weight: 800;
  background: linear-gradient(135deg, #018ffb 0%, #0369a1 50%, #1e40af 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1;
  margin-bottom: 20px;
  text-shadow: 0 4px 16px rgba(1, 143, 251, 0.2);
  position: relative;
}

.score-value::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 120px;
  background: radial-gradient(circle, rgba(1, 143, 251, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  z-index: -1;
}

.score-rate {
  margin-bottom: 16px;
}

:deep(.ant-rate-star) {
  font-size: 20px;
}

.score-summary {
  color: #6b7280;
  font-size: 14px;
  text-align: center;
}

.score-stars {
  padding-left: 20px;
}

.star-bar {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  height: 24px;
}

.star-label {
  width: 36px;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  text-align: right;
}

.star-progress {
  flex: 1;
  margin: 0 !important;
}

:deep(.ant-progress-bg) {
  height: 14px !important;
  border-radius: 8px !important;
  background: linear-gradient(135deg, #018ffb 0%, #0369a1 100%) !important;
  box-shadow: 0 2px 8px rgba(1, 143, 251, 0.3) !important;
}

:deep(.ant-progress-inner) {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
  border-radius: 8px !important;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06) !important;
}

.star-count {
  width: 60px;
  text-align: right;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
}

/* 评分趋势区域样式 */

.trend-chart {
  width: 100%;
  height: 450px;
  border-radius: 8px;
  margin-top: 16px;
}

.trend-chart-container {
  width: 100%;
  height: 100%;
}

/* 评论详情区域样式 */
.comment-section {
  height: 945px; /* 固定高度，您可以调整这个数值 */
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许缩小 */
}

/* 评论详情标题样式 */
.comment-title-section {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}
.comment-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  border-bottom: 4px solid #018ffb;
  padding-bottom: 4px;
  display: inline-block;
  width: fit-content;
  max-width: 100px;
}
/* 评论筛选行样式 */
.comment-filter-row {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.comment-search-row {
  margin-bottom: 16px;
  display: flex;
  gap: 12px; /* 设置输入框和按钮之间的间距 */
  align-items: center;
}

.comment-keyword-input {
  flex: 1; /* 输入框占据剩余空间 */
  border-radius: 6px; /* 恢复完整圆角 */
}

.comment-search-btn {
  flex-shrink: 0; /* 按钮不缩小 */
  min-width: 80px; /* 按钮最小宽度 */
}

/* Ant Design Tabs 样式覆盖 */
.comment-tabs {
  margin-bottom: 16px;
}

:deep(.ant-tabs-nav) {
  margin-bottom: 16px;
}

:deep(.ant-tabs-tab) {
  padding: 8px 16px;
  font-size: 14px;
}

:deep(.ant-tabs-tab-active) {
  color: #018ffb;
}

:deep(.ant-tabs-ink-bar) {
  background: #018ffb;
}

.comment-more {
  color: #018ffb;
  font-size: 14px;
  padding: 0;
  height: auto;
  border: none;
  box-shadow: none;
}

.comment-more:hover {
  color: #0369a1;
}

/* Ant Design List 样式覆盖 */
.comment-list {
  flex: 1;
  overflow-y: auto;
  min-height: 0; /* 允许flex子元素缩小 */
}

/* 分页组件样式 */
.comment-pagination {
  margin-top: 16px;
  text-align: center;
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
}

:deep(.ant-list-item) {
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
}

:deep(.ant-list-item:last-child) {
  border-bottom: none;
}

:deep(.ant-list-item-meta) {
  margin-bottom: 0;
}

:deep(.ant-list-item-meta-description) {
  margin-bottom: 0;
}

.comment-content {
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
  margin-bottom: 8px;
}

.comment-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comment-rating {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rating-text {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.comment-date {
  color: #9ca3af;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content :deep(.ant-col) {
    width: 100% !important;
    flex: 0 0 100% !important;
    max-width: 100% !important;
  }

  .score-main {
    flex-direction: column;
  }

  .score-value-section {
    margin-bottom: 20px;
  }

  .score-stars {
    padding-left: 0;
  }

  :deep(.ant-card-extra) {
    flex-wrap: wrap;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }

  :deep(.ant-card-body) {
    padding: 16px;
  }

  .score-value {
    font-size: 48px;
  }

  .star-bar {
    margin-bottom: 8px;
  }
}

/* 滚动条样式 */
.comment-list::-webkit-scrollbar {
  width: 6px;
}

.comment-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.comment-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.comment-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 额外的 Ant Design 组件样式调整 */
:deep(.ant-space-compact) {
  width: 100%;
}

:deep(.ant-input-group-compact) {
  display: flex;
  width: 100%;
}

:deep(.ant-btn-primary) {
  background-color: #018ffb;
  border-color: #018ffb;
}

:deep(.ant-btn-primary:hover) {
  background-color: #0369a1;
  border-color: #0369a1;
}

:deep(.ant-select-selector) {
  border-radius: 6px;
}

:deep(.ant-picker) {
  border-radius: 6px;
}


:deep(.ant-input) {
  border-radius: 6px;
}

/* 保留的动画效果 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

.score-value {
  animation: pulse 2s ease-in-out infinite;
}

/* 星级评分动画 */
:deep(.ant-rate-star) {
  transition: all 0.3s ease;
}

:deep(.ant-rate-star:hover) {
  transform: scale(1.1);
}

/* 进度条动画 */
:deep(.ant-progress-bg) {
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
}
</style>
