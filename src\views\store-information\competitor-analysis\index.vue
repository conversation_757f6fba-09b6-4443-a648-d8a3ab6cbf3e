<template>
  <!-- 顶部筛选条件区域 -->
  <div class="filter-section">
    <a-row :gutter="[16, 16]" align="middle">
      <a-col :xs="24" :sm="12" :md="8" :lg="6">
        <a-range-picker
          :presets="rangePresets"
          @change="onRangeChange"
          v-model:value="selectedRange"
          style="width: 100%;"
        />
      </a-col>

      <a-col :xs="24" :sm="12" :md="8" :lg="6">
        <a-select
          v-model:value="selectedCountry"
          mode="multiple"
          allowClear
          placeholder="请选择国家/地区"
          style="width: 100%;"
          :max-tag-count="1"
          :max-tag-placeholder="maxTagPlaceholder"
          @change="handleCountryChange"
        >
          <a-select-option value="all" @click="selectAllCountries">选择全部</a-select-option>
          <a-select-option v-for="country in countries" :key="country.value" :value="country.value">
            {{ country.label }}
          </a-select-option>
        </a-select>
      </a-col>

      <a-col :xs="24" :sm="12" :md="8" :lg="6">
        <a-select
          v-model:value="selectedPlatform"
          mode="multiple"
          allowClear
          placeholder="请选择平台"
          style="width: 100%;"
          :max-tag-count="1"
          :max-tag-placeholder="maxTagPlaceholder"
          @change="handlePlatformChange"
        >
          <a-select-option value="all" @click="selectAllPlatforms">选择全部</a-select-option>
          <a-select-option v-for="device in devices" :key="device.value" :value="device.value">
            {{ device.label }}
          </a-select-option>
        </a-select>
      </a-col>
    </a-row>
  </div>

  <!-- 主要内容区域 -->
  <div class="main-content">
    <a-row :gutter="[16, 16]">
      <a-col :xs="24" :sm="24" :md="24" :lg="8" :xl="6" class="left_card">
        <div class="mode-selector">
          <a-select
            v-model:value="compareMode"
            style="width: 100%;"
            :options="[
              { label: '发行商维度', value: 'download' },
              { label: '游戏维度', value: 'income' }
            ]"
            @change="handleModeChange"
          ></a-select>
        </div>
        <!-- 发行商/游戏卡片区，支持删除 -->
        <div class="cards-container">
          <div class="app_card" v-for="(item,index) in card_data" :key="item.name">
            <div class="card-content">
              <div class="card-checkbox">
                <a-checkbox v-model:checked="item.checked"></a-checkbox>
              </div>
              <div class="card-image">
                <img :src="item.img" alt="" class="card-img">
              </div>
              <div class="card-info">
                <div class="card-name" :title="item.name">
                  {{ item.name.length > 10 ? item.name.slice(0, 10) + '…' : item.name }}
                </div>
                <div class="card-component" :title="item.component_name">
                  {{ item.component_name.length > 10 ? item.component_name.slice(0, 10) + '…' : item.component_name }}
                </div>
              </div>
              <div class="card-action">
                <a-button type="link" @click="handleDelete(index)" size="small">删除</a-button>
              </div>
            </div>
          </div>
          <div class="app_card add-card" @click="open_add">
            <div class="card-content">
              <div class="add-icon">
                <img src="./add.png" alt="" class="add-img">
              </div>
              <div class="add-text">添加{{ webb_name }}</div>
            </div>
          </div>
        </div>

        <div class="action-buttons">
          <a-button type="primary" @click="data_ok" block>查询</a-button>
        </div>

        <div class="control-section">
          <div class="control-group">
            <div class="control-title">日期颗粒度</div>
            <a-radio-group v-model:value="data_type" class="control-radio">
              <a-radio-button value="day_0">自动（日）</a-radio-button>
              <a-radio-button value="day">日</a-radio-button>
              <a-radio-button value="week">周</a-radio-button>
              <a-radio-button value="month">月</a-radio-button>
            </a-radio-group>
          </div>

          <div class="control-group">
            <div class="control-title">比较指标</div>
            <a-radio-group v-model:value="compare_index" class="control-radio">
              <a-radio-button value="下载量">下载量</a-radio-button>
              <a-radio-button value="净收入">净收入</a-radio-button>
            </a-radio-group>
          </div>
        </div>
      </a-col>
      <a-col :xs="24" :sm="24" :md="24" :lg="16" :xl="18" class="right_card">
    <div style="border-left: 3px solid #29ade6;margin-left: 25px;margin-top: 20px;padding-left: 9px;font-size: 16px;font-weight: bold;">
      查看{{ compare_index === '下载量' ? '下载量' : '净收入' }}
    </div>
        <div class="chart-container">
          <div v-if="!hasChartData" class="empty-data-container">
            <i class="empty-icon">📊</i>
            <p>暂无对比数据</p>
            <p class="empty-data-tip">请选择要对比的{{ webb_name }}并点击查询按钮</p>
          </div>
          <LineChart
            v-else
            :chartData="lineChartData"
            :width="'100%'"
            :height="'500px'"
          />
        </div>
      </a-col>
    </a-row>
  </div>
<div class="px-10">
  <Modal @register="register" />
  <GameSelectModal @register="registerGameModal" @ok="onGameAdd" />
  <From_component @register="registerPublisherModal" @ok="onPublisherAdd" />
</div>
</template>

<script  lang="ts" setup>
import { ActionItem, BasicTable } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage';
import {columns} from './component/From_component.data'
import {ref,onMounted,defineComponent, onUnmounted, h } from 'vue';
import { SearchOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import type { CascaderProps,SelectProps } from 'ant-design-vue';
import { Cascader } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import * as echarts from 'echarts';
import { useModal } from '/@/components/Modal';
import Modal from './addModel.vue';
import { message } from 'ant-design-vue';
import GameSelectModal from './component/GameSelectModal.vue';
import From_component from './addModel.vue';
import { getAllDeviceApi, getDownloadCompareApi, getPublisherInfo3Api, getAllCountryApi, gameDimensionApi } from '/@/api/store-information/competitor-analysis/index';
import LineChart from '/@/views/public-opinion-monitoring/components/chart/LineChart.vue';

const webb_name=ref("发行商")
// 时间选择框的便捷选项设置
type RangeValue = [Dayjs, Dayjs];

const rangePresets = ref([
{ label: '当天', value: [dayjs().add(-1, 'd'), dayjs()] },
{ label: '最近三天', value: [dayjs().add(-3, 'd'), dayjs()] },
{ label: '最近一周', value: [dayjs().add(-7, 'd'), dayjs()] },
{ label: '最近一个月', value: [dayjs().add(-1, 'month'), dayjs()] },
{ label: '最近三个月', value: [dayjs().add(-3, 'month'), dayjs()] },
{ label: '最近六个月', value: [dayjs().add(-6, 'month'), dayjs()] },
{ label: '最近一年', value: [dayjs().add(-1, 'year'), dayjs()] },
{ label: '最近两年', value: [dayjs().add(-2, 'year'), dayjs()] },
{ label: '最近三年', value: [dayjs().add(-3, 'year'), dayjs()] },
]);

// 设置默认选中2025年4月19日
const selectedRange = ref<RangeValue>([dayjs('2025-04-19'), dayjs('2025-04-19')]);

const onRangeChange = (dates: RangeValue, dateStrings: string[]) => {
if (dates) {
  console.log('From: ', dates[0], ', to: ', dates[1]);
  console.log('From: ', dateStrings[0], ', to: ', dateStrings[1]);
} else {
  console.log('Clear');
}
};
// 设备选择的数据
  const equipment_data=ref([
    {
      label: 'App Store',
      value: 'apple',
    }, 
    // {
    //   label: 'Google Play',
    //   value: 'google',
    // },
  ])
  // 国家地区的选择
  const select_country = ref<{ value: string; label: string }[]>([]);
  // 选择后的设备数据
  const equipment = ref<string[]>([]);
  // 选择设备多选框
  const options: CascaderProps['options'] = equipment_data.value
  const country_1= ref<SelectProps['options']>(select_country.value);
  const country_data = ref([]);
// 下载量，净收入的结果
  const value2 = ref('下载量');
  const options2 = ref<SelectProps['options']>([
  {
      value: '下载量',
      label: '下载量',
  },
  {
      value: '净收入',
      label: '净收入',
  },
  ]);



// 时间颗粒度
const data_type=ref("day_0");
// 比较指标
const compare_index=ref("下载量");

// 折线图数据
const lineChartData = ref<any>({});
// 是否有图表数据
const hasChartData = ref(false);


// 柱状图
let myChart = ref()
  let option = ref({})

  onMounted(async () => {
    fetchDevices();
    fetchCountries();
  })
const init = (data) => {
  // 基于准备好的dom，初始化echarts实例
  myChart.value = echarts.init(document.getElementById('data_echar'));
  // 绘制图表
  option.value = {
    legend: {},
    tooltip: {},
    dataset: {
      source: data
    },
    xAxis: { type: 'category' },
    yAxis: {},
    // Declare several bar series, each will be mapped
    // to a column of dataset.source by default.
    series: [{ type: 'bar' }, { type: 'bar' }, { type: 'bar' }]
  };
  myChart.value.setOption(option.value)
}

// 游戏/发行商卡片数据类型定义
interface CardItem {
  name: string;
  component_name: string;
  img: string;
  checked: boolean;
}

// 游戏/发行商卡片数据
const card_data = ref<CardItem[]>([
// 示例数据，实际添加游戏后会 push 进来
]);

// 发行商选择回调
const onPublisherAdd = (selectedPublishers: any) => {
if (!selectedPublishers) {
  return;
}
const publishers = Array.isArray(selectedPublishers) ? selectedPublishers : [selectedPublishers];

// 检查是否超过最大数量限制
if (card_data.value.length + publishers.length > 5) {
  message.warning('最多只能添加5个发行商');
  return;
}

// 检查是否有重复发行商
const duplicatePublishers = publishers.filter(publisher => 
  card_data.value.some(existingPublisher => existingPublisher.name === publisher.name)
);

if (duplicatePublishers.length > 0) {
  message.warning(`发行商 ${duplicatePublishers.map(p => p.name).join('、')} 已存在，不能重复添加`);
  return;
}

publishers.forEach((publisher) => {
  if (publisher && publisher.name) {
    card_data.value.push({
      name: publisher.name,
      component_name: publisher.component_name || publisher.name,
      img: publisher.img || new URL('./toux.png', import.meta.url).href,
      checked: true
    });
  }
});
};

// 删除发行商
function deletePublisher(index) {
card_data.value.splice(index, 1);
}



// 数据转换函数：将API返回的数据转换为LineChart组件需要的格式
const transformToLineChartData = (apiData, metric) => {
  if (!apiData || !Array.isArray(apiData)) {
    return {
      xAxisData: [],
      seriesData: [],
      legendData: [],
      yAxisName: metric === '下载量' ? '下载量' : '收入($)'
    };
  }

  // 提取所有时间点
  const allTimePoints = new Set();
  apiData.forEach(game => {
    if (game.period && Array.isArray(game.period)) {
      game.period.forEach(item => {
        allTimePoints.add(item.time);
      });
    }
  });

  // 排序时间点
  const sortedTimePoints = Array.from(allTimePoints).sort();

  // 转换数据格式
  const seriesData = apiData.map(game => {
    const data = sortedTimePoints.map(time => {
      const periodItem = game.period?.find(p => p.time === time);
      if (metric === '下载量') {
        return periodItem ? periodItem.downloads || 0 : 0;
      } else {
        return periodItem ? periodItem.revenues || 0 : 0;
      }
    });

    return {
      name: game.gameName || game.appId,
      data: data
    };
  });

  // 提取图例数据
  const legendData = apiData.map(game => game.gameName || game.appId);

  return {
    xAxisData: sortedTimePoints,
    seriesData: seriesData,
    legendData: legendData,
    yAxisName: metric === '下载量' ? '下载量' : '收入($)'
  };
};

// 查询数据
const data_ok = async () => {
  // 检查是否选择了游戏
  if (!card_data.value || card_data.value.length === 0) {
    message.warning('请先选择要对比的游戏');
    return;
  }

  // 检查是否选择了时间范围
  if (!selectedRange.value || selectedRange.value.length !== 2) {
    message.warning('请选择时间范围');
    return;
  }

  // 修复颗粒度映射
  let granularity: 'day' | 'week' | 'month';
  if (data_type.value === 'day_0' || data_type.value === 'day') {
    granularity = 'day';
  } else if (data_type.value === 'week') {
    granularity = 'week';
  } else if (data_type.value === 'month') {
    granularity = 'month';
  } else {
    granularity = 'day'; // 默认值
  }

  // 构建API参数 - appIdList应该传递游戏的appId，不是游戏名称
  const params = {
    countryNames: selectedCountry.value.length > 0 ? selectedCountry.value : [],
    platformNames: selectedPlatform.value.length > 0 ? selectedPlatform.value : [],
    appIdList: card_data.value.map(game => game.component_name), // 使用component_name作为appId
    startTime: dayjs(selectedRange.value[0]).format('YYYY-MM-DD'),
    endTime: dayjs(selectedRange.value[1]).format('YYYY-MM-DD'),
    granularity: granularity
  };

  console.log('调用游戏维度API，参数:', params);

  try {
    const res = await gameDimensionApi(params);
    console.log('游戏维度接口返回数据:', res);

    // 检查返回数据
    if (res && res.result && Array.isArray(res.result) && res.result.length > 0) {
      // 转换数据为折线图格式
      lineChartData.value = transformToLineChartData(res.result, compare_index.value);
      hasChartData.value = true;
    } else {
      hasChartData.value = false;
      message.warning('暂无数据');
    }
  } catch (error) {
    console.error('获取游戏数据失败:', error);
    message.error('获取游戏数据失败');
    hasChartData.value = false;
  }


};

// 监听窗口大小变化，调整图表大小
window.addEventListener('resize', () => {
if (myChart.value) {
  myChart.value.resize();
}
});

// 组件卸载时清理
onUnmounted(() => {
if (myChart.value) {
  myChart.value.dispose();
  myChart.value = null;
}
window.removeEventListener('resize', () => {
  if (myChart.value) {
    myChart.value.resize();
  }
});
});

// Modal 注册回调
const [register, { openModal }] = useModal();

// 对比模式
const compareMode = ref<'download' | 'income'>('download');
const isPublisherMode = ref(true);

// 处理模式切换
const handleModeChange = (value: 'download' | 'income') => {
compareMode.value = value;
switchMode(value);
};

// 切换模式
const switchMode = (mode: 'download' | 'income') => {
// 清空已选择的数据
card_data.value = [];
// 更新维度名称
webb_name.value = mode === 'download' ? '发行商' : '游戏';
// 更新图表标题
if (option.value && option.value.title) {
  option.value.title.text = mode === 'download' ? '发行商下载量对比' : '游戏净收入对比';
  // 更新图表Y轴名称
  if (option.value.yAxis) {
    option.value.yAxis.name = mode === 'download' ? '下载量' : '净收入';
  }
  // 更新图表数据
  if (option.value.series && option.value.series[0]) {
    option.value.series[0].name = mode === 'download' ? '下载量' : '净收入';
  }
  // 更新图表
  myChart.value?.setOption(option.value);
}
};

// 打开添加模态框
const open_add = () => {
if (compareMode.value === 'download') {
  openPublisherModal(true, {
    webb_name: webb_name.value,
    existingPublishers: card_data.value, // 传入已存在的发行商列表
    onOk: onPublisherAdd
  });
} else {
  openGameModal(true, {
    webb_name: webb_name.value,
    onOk: onGameAdd
  });
}
};

// 游戏选择回调
const onGameAdd = (selectedGames: any) => {
if (!selectedGames) {
  return;
}
const games = Array.isArray(selectedGames) ? selectedGames : [selectedGames];

// 检查是否超过最大数量限制
if (card_data.value.length + games.length > 5) {
  message.warning('最多只能添加5个游戏');
  return;
}

// 检查是否有重复游戏
const duplicateGames = games.filter(game => 
  card_data.value.some(existingGame => existingGame.name === game.nameZh)
);

if (duplicateGames.length > 0) {
  message.warning(`游戏 ${duplicateGames.map(g => g.nameZh).join('、')} 已存在，不能重复添加`);
  return;
}

games.forEach((game) => {
  if (game && game.nameZh) {
    card_data.value.push({
      name: game.nameZh,
      component_name: game.id || game.gameId || game.nameZh, // 使用游戏的id作为appId
      img: game.iconUrl || new URL('./toux.png', import.meta.url).href,
      checked: true
    });
  }
});
};

// 国家和平台数据结构与 download-analysis 保持一致
const selectedCountry = ref<string[]>([]);
const selectedPlatform = ref<string[]>([]);
const selectedGenre = ref<string[]>([]);

const countries = ref<{ value: string; label: string }[]>([]);

const devices = ref<{ value: string; label: string }[]>([]);
const fetchDevices = async () => {
try {
  const res = await getAllDeviceApi();
  devices.value = [
    ...(res || []).map((item: any) => ({
      value: item.value,
      label: item.value === 'apple' ? 'App Store' : item.value,
    }))
  ];
} catch (e) {
  devices.value = [];
}
};

// 获取国家数据
const fetchCountries = async () => {
  try {
    const res = await getAllCountryApi();

    if (res && Array.isArray(res)) {
      countries.value = res
        .filter((item: any) => {
          return item &&
                 typeof item === 'object' &&
                 item.value &&
                 typeof item.value === 'string' &&
                 item.value.trim() !== '' &&
                 item.value !== '5' &&
                 item.value !== '6'; // 过滤掉数字ID
        })
        .map((item: any) => ({
          value: item.value.trim(),
          label: item.value.trim(),
        }));
    } else {
      throw new Error('API返回数据格式不正确');
    }
  } catch (e) {
    // 如果API失败，使用默认数据
    countries.value = [
      { value: '菲律宾', label: '菲律宾' },
      { value: '柬埔寨', label: '柬埔寨' },
      { value: '马来西亚', label: '马来西亚' },
      { value: '泰国', label: '泰国' },
      { value: '文莱', label: '文莱' },
      { value: '新加坡', label: '新加坡' },
      { value: '印度尼西亚', label: '印度尼西亚' },
      { value: '越南', label: '越南' },
      { value: '缅甸', label: '缅甸' },
      { value: '中国台湾', label: '中国台湾' },
      { value: '老挝人民民主共和国', label: '老挝人民民主共和国' },
    ];
  }
};


const checkedList = ref(['下载量', '收入']); // 设置默认选中的值

// 注册游戏选择模态框
const [registerGameModal, { openModal: openGameModal }] = useModal();

// 注册发行商选择模态框
const [registerPublisherModal, { openModal: openPublisherModal }] = useModal();

// 删除游戏
const handleDelete = (index: number) => {
if (compareMode.value === 'income') {
  // 游戏维度时，从列表中删除游戏
  card_data.value.splice(index, 1);
} else {
  // 发行商维度时，从列表中删除发行商
  card_data.value.splice(index, 1);
}
};

// 添加标签溢出处理函数
const maxTagPlaceholder = (omittedValues: any[]) => {
return h('span', { class: 'ellipsis-tag' }, '...');
};

// 添加选择全部和处理函数
const selectAllCountries = () => {
selectedCountry.value = countries.value.map(item => item.value);
};
const handleCountryChange = (value: string[]) => {
if (value.includes('all')) {
  selectedCountry.value = countries.value.map(item => item.value);
}
};

const selectAllPlatforms = () => {
selectedPlatform.value = devices.value.map(item => item.value);
};
const handlePlatformChange = (value: string[]) => {
if (value.includes('all')) {
  selectedPlatform.value = devices.value.map(item => item.value);
}
};

const selectAllGenres = () => {
selectedGenre.value = checkedList.value;
};
const handleGenreChange = (value: string[]) => {
if (value.includes('all')) {
  selectedGenre.value = checkedList.value;
}
};



// 修改处理函数
const handleCardClick = (item: CardItem) => {
item.checked = !item.checked;
if (item.checked) {
  card_data.value.push(item);
} else {
  const index = card_data.value.findIndex(card => card.component_name === item.component_name);
  if (index !== -1) {
    card_data.value.splice(index, 1);
  }
}
};
</script>

<style scoped>
/* 顶部筛选区域 */
.filter-section {
  padding: 16px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 3px 7px rgba(77, 85, 117, 0.05);
  margin-bottom: 16px;
}

/* 主要内容区域 */
.main-content {
  padding: 0 16px;
}

/* 左侧卡片 */
.left_card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 3px 7px rgba(77, 85, 117, 0.05);
  padding: 16px;
  min-height: 600px;
}

/* 右侧卡片 */
.right_card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 3px 7px rgba(77, 85, 117, 0.05);
  padding: 16px;
  min-height: 600px;
}

/* 模式选择器 */
.mode-selector {
  margin-bottom: 16px;
}

/* 卡片容器 */
.cards-container {
  margin-bottom: 16px;
}

/* 应用卡片 */
.app_card {
  background-color: #e2e8eb;
  border-radius: 8px;
  margin-bottom: 8px;
  min-height: 60px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.app_card:hover {
  background-color: #d1d9dc;
}

.add-card {
  background-color: #f0f0f0;
  border: 2px dashed #d9d9d9;
}

.add-card:hover {
  border-color: #1890ff;
  background-color: #f6ffed;
}

/* 卡片内容 */
.card-content {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  height: 100%;
}

.card-checkbox {
  flex-shrink: 0;
  margin-right: 8px;
}

.card-image {
  flex-shrink: 0;
  margin-right: 12px;
}

.card-img {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: cover;
}

.card-info {
  flex: 1;
  min-width: 0;
}

.card-name {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-component {
  font-size: 12px;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-action {
  flex-shrink: 0;
}

/* 添加卡片样式 */
.add-icon {
  flex-shrink: 0;
  margin-right: 12px;
}

.add-img {
  width: 24px;
  height: 24px;
}

.add-text {
  flex: 1;
  font-size: 14px;
  color: #666;
}

/* 操作按钮 */
.action-buttons {
  margin-bottom: 24px;
}

/* 控制区域 */
.control-section {
  margin-top: 24px;
}

.control-group {
  margin-bottom: 20px;
}

.control-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #333;
}

.control-radio {
  width: 100%;
}

.control-radio .ant-radio-button-wrapper {
  flex: 1;
  text-align: center;
}

/* 图表容器 */
.chart-container {
  padding: 16px 0;
}

.chart-content {
  width: 100%;
  height: 500px;
  min-height: 400px;
}
.empty-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  border-radius: 8px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-data-container p {
  margin: 0;
  font-size: 16px;
}

.empty-data-tip {
  margin-top: 8px;
  font-size: 14px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .chart-content {
    height: 400px;
  }

  .control-radio .ant-radio-button-wrapper {
    font-size: 12px;
    padding: 4px 8px;
  }
}

@media (max-width: 992px) {
  .main-content {
    padding: 0 8px;
  }

  .left_card, .right_card {
    margin-bottom: 16px;
  }

  .chart-content {
    height: 350px;
  }

  .card-name {
    font-size: 13px;
  }

  .card-component {
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .filter-section {
    padding: 12px;
  }

  .left_card, .right_card {
    padding: 12px;
    min-height: auto;
  }

  .chart-content {
    height: 300px;
  }

  .control-radio .ant-radio-button-wrapper {
    font-size: 11px;
    padding: 2px 4px;
  }

  .card-content {
    padding: 6px 8px;
  }

  .card-img {
    width: 32px;
    height: 32px;
  }

  .app_card {
    min-height: 50px;
  }
}

@media (max-width: 576px) {
  .main-content {
    padding: 0 4px;
  }

  .filter-section {
    padding: 8px;
  }

  .chart-content {
    height: 250px;
  }

  .control-title {
    font-size: 14px;
  }

  .card-name {
    font-size: 12px;
  }

  .card-component {
    font-size: 10px;
  }

  .control-radio {
    display: flex;
    flex-wrap: wrap;
  }

  .control-radio .ant-radio-button-wrapper {
    flex: 1 1 50%;
    min-width: 50%;
  }
}
</style>