<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="选择游戏"
    @ok="handleSubmit"
    width="800px"
  >
    <div class="search-box">
      <a-input-search
        v-model:value="searchKeyword"
        placeholder="请输入游戏名称搜索"
        style="width: 100%"
        @search="handleSearch"
      />
    </div>
    
    <div class="game-list">
      <!-- 无搜索结果提示 -->
      <div v-if="gameList.length === 0 && !loading" class="empty-state">
        <div class="empty-icon">😔</div>
        <div class="empty-text">未找到相关游戏</div>
        <div class="empty-subtext" v-if="searchKeyword.trim()">请尝试其他关键词</div>
      </div>

      <!-- 游戏列表 -->
      <a-list
        v-else
        :data-source="gameList"
        :loading="loading"
        :bordered="false"
      >
        <template #renderItem="{ item }">
          <a-list-item>
            <a-checkbox
              :checked="selectedRowKeys.includes(item.id)"
              @change="(e) => handleGameSelect(e, item)"
            >
              <div class="game-item">
                <img :src="item.iconUrl || defaultIcon" class="game-icon" />
                <span class="game-name">{{ item.nameZh }}</span>
              </div>
            </a-checkbox>
          </a-list-item>
        </template>
      </a-list>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-more">
        <a-spin />
      </div>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { message } from 'ant-design-vue';
import { findGamesByPrefixApi } from '/@/api/public-opinion-monitoring/index';

// 修改默认图标的导入
const defaultIcon = new URL('../toux.png', import.meta.url).href;

const emit = defineEmits(['register', 'ok', 'cancel']);
const [registerModal, { closeModal }] = useModalInner((data) => {
  // 初始化数据
  selectedRowKeys.value = [];
  searchKeyword.value = '';
  fetchGameList(); // 自动加载游戏列表
});

const searchKeyword = ref('');
const gameList = ref<any[]>([]);
const selectedRowKeys = ref<string[]>([]);
const loading = ref(false);

// 获取游戏列表
const fetchGameList = async () => {
  if (loading.value) return;

  loading.value = true;
  try {
    const res = await findGamesByPrefixApi({
      prefix: searchKeyword.value.trim() || '' // 使用空前缀来获取所有游戏
    });

    // 根据API返回的数据结构调整
    gameList.value = res || [];
  } catch (e) {
    console.error('获取游戏列表失败:', e);
    message.error('获取游戏列表失败');
    gameList.value = [];
  } finally {
    loading.value = false;
  }
};



// 搜索处理
const handleSearch = (value: string) => {
  searchKeyword.value = value;
  fetchGameList();
};

// 选择处理
const handleGameSelect = (e: any, game: any) => {
  if (e.target.checked) {
    if (selectedRowKeys.value.length >= 5) {
      message.warning('最多只能选择5款游戏');
      e.target.checked = false;
      return;
    }
    selectedRowKeys.value.push(game.id);
  } else {
    selectedRowKeys.value = selectedRowKeys.value.filter(id => id !== game.id);
  }
};

// 确认选择
const handleSubmit = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择游戏');
    return;
  }
  const selectedGames = gameList.value.filter(game => selectedRowKeys.value.includes(game.id));
  emit('ok', selectedGames);
  closeModal();
};

// 初始化
onMounted(() => {
  fetchGameList();
});
</script>

<style scoped>
.search-box {
  margin-bottom: 16px;
}

.game-list {
  height: 400px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.game-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.game-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  border-radius: 4px;
}

.game-name {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.loading-more {
  text-align: center;
  padding: 12px 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  margin-bottom: 8px;
  color: #666;
}

.empty-subtext {
  font-size: 14px;
  color: #999;
}

:deep(.ant-list-item) {
  padding: 4px 16px;
}

:deep(.ant-checkbox-wrapper) {
  width: 100%;
}

:deep(.ant-list-item:hover) {
  background-color: #f5f5f5;
}
</style> 